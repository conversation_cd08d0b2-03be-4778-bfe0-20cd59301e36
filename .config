
#
# RT-Thread Kernel
#

#
# klibc options
#

#
# rt_vsnprintf options
#
# CONFIG_RT_KLIBC_USING_LIBC_VSNPRINTF is not set
CONFIG_RT_KLIBC_USING_VSNPRINTF_LONGLONG=y
# CONFIG_RT_KLIBC_USING_VSNPRINTF_STANDARD is not set
# end of rt_vsnprintf options

#
# rt_vsscanf options
#
# CONFIG_RT_KLIBC_USING_LIBC_VSSCANF is not set
# end of rt_vsscanf options

#
# rt_memset options
#
# CONFIG_RT_KLIBC_USING_USER_MEMSET is not set
# CONFIG_RT_KLIBC_USING_LIBC_MEMSET is not set
# CONFIG_RT_KLIBC_USING_TINY_MEMSET is not set
# end of rt_memset options

#
# rt_memcpy options
#
# CONFIG_RT_KLIBC_USING_USER_MEMCPY is not set
# CONFIG_RT_KLIBC_USING_LIBC_MEMCPY is not set
# CONFIG_RT_KLIBC_USING_TINY_MEMCPY is not set
# end of rt_memcpy options

#
# rt_memmove options
#
# CONFIG_RT_KLIBC_USING_USER_MEMMOVE is not set
# CONFIG_RT_KLIBC_USING_LIBC_MEMMOVE is not set
# end of rt_memmove options

#
# rt_memcmp options
#
# CONFIG_RT_KLIBC_USING_USER_MEMCMP is not set
# CONFIG_RT_KLIBC_USING_LIBC_MEMCMP is not set
# end of rt_memcmp options

#
# rt_strstr options
#
# CONFIG_RT_KLIBC_USING_USER_STRSTR is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRSTR is not set
# end of rt_strstr options

#
# rt_strcasecmp options
#
# CONFIG_RT_KLIBC_USING_USER_STRCASECMP is not set
# end of rt_strcasecmp options

#
# rt_strncpy options
#
# CONFIG_RT_KLIBC_USING_USER_STRNCPY is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRNCPY is not set
# end of rt_strncpy options

#
# rt_strcpy options
#
# CONFIG_RT_KLIBC_USING_USER_STRCPY is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRCPY is not set
# end of rt_strcpy options

#
# rt_strncmp options
#
# CONFIG_RT_KLIBC_USING_USER_STRNCMP is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRNCMP is not set
# end of rt_strncmp options

#
# rt_strcmp options
#
# CONFIG_RT_KLIBC_USING_USER_STRCMP is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRCMP is not set
# end of rt_strcmp options

#
# rt_strlen options
#
# CONFIG_RT_KLIBC_USING_USER_STRLEN is not set
# CONFIG_RT_KLIBC_USING_LIBC_STRLEN is not set
# end of rt_strlen options

#
# rt_strnlen options
#
# CONFIG_RT_KLIBC_USING_USER_STRNLEN is not set
# end of rt_strnlen options

# CONFIG_RT_UTEST_TC_USING_KLIBC is not set
# end of klibc options

CONFIG_RT_NAME_MAX=8
# CONFIG_RT_USING_ARCH_DATA_TYPE is not set
# CONFIG_RT_USING_NANO is not set
# CONFIG_RT_USING_AMP is not set
# CONFIG_RT_USING_SMP is not set
CONFIG_RT_CPUS_NR=1
CONFIG_RT_ALIGN_SIZE=4
# CONFIG_RT_THREAD_PRIORITY_8 is not set
CONFIG_RT_THREAD_PRIORITY_32=y
# CONFIG_RT_THREAD_PRIORITY_256 is not set
CONFIG_RT_THREAD_PRIORITY_MAX=32
CONFIG_RT_TICK_PER_SECOND=1000
CONFIG_RT_USING_OVERFLOW_CHECK=y
CONFIG_RT_USING_HOOK=y
CONFIG_RT_HOOK_USING_FUNC_PTR=y
# CONFIG_RT_USING_HOOKLIST is not set
CONFIG_RT_USING_IDLE_HOOK=y
CONFIG_RT_IDLE_HOOK_LIST_SIZE=4
CONFIG_IDLE_THREAD_STACK_SIZE=256
CONFIG_RT_USING_TIMER_SOFT=y
CONFIG_RT_TIMER_THREAD_PRIO=4
CONFIG_RT_TIMER_THREAD_STACK_SIZE=2048
# CONFIG_RT_USING_TIMER_ALL_SOFT is not set
CONFIG_RT_USING_CPU_USAGE_TRACER=y

#
# kservice options
#
# CONFIG_RT_USING_TINY_FFS is not set
# end of kservice options

CONFIG_RT_USING_DEBUG=y
CONFIG_RT_DEBUGING_ASSERT=y
CONFIG_RT_DEBUGING_COLOR=y
CONFIG_RT_DEBUGING_CONTEXT=y
# CONFIG_RT_DEBUGING_AUTO_INIT is not set
# CONFIG_RT_USING_CI_ACTION is not set

#
# Inter-Thread communication
#
CONFIG_RT_USING_SEMAPHORE=y
CONFIG_RT_USING_MUTEX=y
CONFIG_RT_USING_EVENT=y
# CONFIG_RT_USING_MAILBOX is not set
CONFIG_RT_USING_MESSAGEQUEUE=y
CONFIG_RT_USING_MESSAGEQUEUE_PRIORITY=y
# CONFIG_RT_USING_SIGNALS is not set
# end of Inter-Thread communication

#
# Memory Management
#
CONFIG_RT_USING_MEMPOOL=y
CONFIG_RT_USING_SMALL_MEM=y
# CONFIG_RT_USING_SLAB is not set
# CONFIG_RT_USING_MEMHEAP is not set
CONFIG_RT_USING_SMALL_MEM_AS_HEAP=y
# CONFIG_RT_USING_MEMHEAP_AS_HEAP is not set
# CONFIG_RT_USING_SLAB_AS_HEAP is not set
# CONFIG_RT_USING_USERHEAP is not set
# CONFIG_RT_USING_NOHEAP is not set
# CONFIG_RT_USING_MEMTRACE is not set
CONFIG_RT_USING_HEAP_ISR=y
CONFIG_RT_USING_HEAP=y
# end of Memory Management

CONFIG_RT_USING_DEVICE=y
# CONFIG_RT_USING_DEVICE_OPS is not set
# CONFIG_RT_USING_INTERRUPT_INFO is not set
# CONFIG_RT_USING_THREADSAFE_PRINTF is not set
CONFIG_RT_USING_CONSOLE=y
CONFIG_RT_CONSOLEBUF_SIZE=128
CONFIG_RT_CONSOLE_DEVICE_NAME="uart2"
CONFIG_RT_VER_NUM=0x50200
# CONFIG_RT_USING_STDC_ATOMIC is not set
CONFIG_RT_BACKTRACE_LEVEL_MAX_NR=32
# end of RT-Thread Kernel

#
# RT-Thread Components
#
CONFIG_RT_USING_COMPONENTS_INIT=y
CONFIG_RT_USING_USER_MAIN=y
CONFIG_RT_MAIN_THREAD_STACK_SIZE=2048
CONFIG_RT_MAIN_THREAD_PRIORITY=10
# CONFIG_RT_USING_LEGACY is not set
CONFIG_RT_USING_MSH=y
CONFIG_RT_USING_FINSH=y
CONFIG_FINSH_USING_MSH=y
CONFIG_FINSH_THREAD_NAME="tshell"
CONFIG_FINSH_THREAD_PRIORITY=20
CONFIG_FINSH_THREAD_STACK_SIZE=4096
CONFIG_FINSH_USING_HISTORY=y
CONFIG_FINSH_HISTORY_LINES=5
CONFIG_FINSH_USING_SYMTAB=y
CONFIG_FINSH_CMD_SIZE=80
CONFIG_MSH_USING_BUILT_IN_COMMANDS=y
CONFIG_FINSH_USING_DESCRIPTION=y
# CONFIG_FINSH_ECHO_DISABLE_DEFAULT is not set
# CONFIG_FINSH_USING_AUTH is not set
CONFIG_FINSH_ARG_MAX=10
CONFIG_FINSH_USING_OPTION_COMPLETION=y

#
# DFS: device virtual file system
#
# CONFIG_RT_USING_DFS is not set
# end of DFS: device virtual file system

CONFIG_RT_USING_FAL=y
CONFIG_FAL_USING_DEBUG=y
CONFIG_FAL_PART_HAS_TABLE_CFG=y
# CONFIG_FAL_USING_SFUD_PORT is not set

#
# Device Drivers
#
# CONFIG_RT_USING_DM is not set
# CONFIG_RT_USING_DEV_BUS is not set
CONFIG_RT_USING_DEVICE_IPC=y
CONFIG_RT_UNAMED_PIPE_NUMBER=64
# CONFIG_RT_USING_SYSTEM_WORKQUEUE is not set
CONFIG_RT_USING_SERIAL=y
CONFIG_RT_USING_SERIAL_V1=y
# CONFIG_RT_USING_SERIAL_V2 is not set
CONFIG_RT_SERIAL_USING_DMA=y
CONFIG_RT_SERIAL_RB_BUFSZ=128
# CONFIG_RT_USING_SERIAL_BYPASS is not set
# CONFIG_RT_USING_CAN is not set
# CONFIG_RT_USING_CPUTIME is not set
CONFIG_RT_USING_I2C=y
# CONFIG_RT_I2C_DEBUG is not set
CONFIG_RT_USING_I2C_BITOPS=y
# CONFIG_RT_I2C_BITOPS_DEBUG is not set
CONFIG_RT_USING_SOFT_I2C=y
CONFIG_RT_USING_SOFT_I2C0=y
CONFIG_RT_SOFT_I2C0_SCL_PIN=92
CONFIG_RT_SOFT_I2C0_SDA_PIN=93
CONFIG_RT_SOFT_I2C0_BUS_NAME="i2c0"
CONFIG_RT_SOFT_I2C0_TIMING_DELAY=5
CONFIG_RT_SOFT_I2C0_TIMING_TIMEOUT=10
CONFIG_RT_USING_SOFT_I2C1=y
CONFIG_RT_SOFT_I2C1_SCL_PIN=15
CONFIG_RT_SOFT_I2C1_SDA_PIN=16
CONFIG_RT_SOFT_I2C1_BUS_NAME="i2c1"
CONFIG_RT_SOFT_I2C1_TIMING_DELAY=5
CONFIG_RT_SOFT_I2C1_TIMING_TIMEOUT=10
# CONFIG_RT_USING_SOFT_I2C2 is not set
# CONFIG_RT_USING_SOFT_I2C3 is not set
# CONFIG_RT_USING_SOFT_I2C4 is not set
# CONFIG_RT_USING_SOFT_I2C5 is not set
# CONFIG_RT_USING_SOFT_I2C6 is not set
# CONFIG_RT_USING_SOFT_I2C7 is not set
# CONFIG_RT_USING_SOFT_I2C8 is not set
# CONFIG_RT_USING_PHY is not set
# CONFIG_RT_USING_PHY_V2 is not set
# CONFIG_RT_USING_ADC is not set
# CONFIG_RT_USING_DAC is not set
# CONFIG_RT_USING_NULL is not set
# CONFIG_RT_USING_ZERO is not set
# CONFIG_RT_USING_RANDOM is not set
# CONFIG_RT_USING_PWM is not set
# CONFIG_RT_USING_PULSE_ENCODER is not set
# CONFIG_RT_USING_INPUT_CAPTURE is not set
# CONFIG_RT_USING_MTD_NOR is not set
# CONFIG_RT_USING_MTD_NAND is not set
# CONFIG_RT_USING_PM is not set
# CONFIG_RT_USING_RTC is not set
# CONFIG_RT_USING_SDIO is not set
# CONFIG_RT_USING_SPI is not set
CONFIG_RT_USING_WDT=y
# CONFIG_RT_USING_AUDIO is not set
# CONFIG_RT_USING_SENSOR is not set
# CONFIG_RT_USING_TOUCH is not set
# CONFIG_RT_USING_LCD is not set
# CONFIG_RT_USING_HWCRYPTO is not set
# CONFIG_RT_USING_WIFI is not set
# CONFIG_RT_USING_BLK is not set
# CONFIG_RT_USING_VIRTIO is not set
CONFIG_RT_USING_PIN=y
# CONFIG_RT_USING_KTIME is not set
CONFIG_RT_USING_HWTIMER=y
# CONFIG_RT_USING_CHERRYUSB is not set
# end of Device Drivers

#
# C/C++ and POSIX layer
#

#
# ISO-ANSI C layer
#

#
# Timezone and Daylight Saving Time
#
# CONFIG_RT_LIBC_USING_FULL_TZ_DST is not set
CONFIG_RT_LIBC_USING_LIGHT_TZ_DST=y
CONFIG_RT_LIBC_TZ_DEFAULT_HOUR=8
CONFIG_RT_LIBC_TZ_DEFAULT_MIN=0
CONFIG_RT_LIBC_TZ_DEFAULT_SEC=0
# end of Timezone and Daylight Saving Time
# end of ISO-ANSI C layer

#
# POSIX (Portable Operating System Interface) layer
#
# CONFIG_RT_USING_POSIX_FS is not set
# CONFIG_RT_USING_POSIX_DELAY is not set
# CONFIG_RT_USING_POSIX_CLOCK is not set
# CONFIG_RT_USING_POSIX_TIMER is not set
# CONFIG_RT_USING_PTHREADS is not set
# CONFIG_RT_USING_MODULE is not set

#
# Interprocess Communication (IPC)
#
# CONFIG_RT_USING_POSIX_PIPE is not set
# CONFIG_RT_USING_POSIX_MESSAGE_QUEUE is not set
# CONFIG_RT_USING_POSIX_MESSAGE_SEMAPHORE is not set

#
# Socket is in the 'Network' category
#
# end of Interprocess Communication (IPC)
# end of POSIX (Portable Operating System Interface) layer

# CONFIG_RT_USING_CPLUSPLUS is not set
# end of C/C++ and POSIX layer

#
# Network
#
# CONFIG_RT_USING_SAL is not set
# CONFIG_RT_USING_NETDEV is not set
# CONFIG_RT_USING_LWIP is not set
# CONFIG_RT_USING_AT is not set
# end of Network

#
# Memory protection
#
# CONFIG_RT_USING_MEM_PROTECTION is not set
# CONFIG_RT_USING_HW_STACK_GUARD is not set
# end of Memory protection

#
# Utilities
#
# CONFIG_RT_USING_RYM is not set
CONFIG_RT_USING_ULOG=y
# CONFIG_ULOG_OUTPUT_LVL_A is not set
# CONFIG_ULOG_OUTPUT_LVL_E is not set
# CONFIG_ULOG_OUTPUT_LVL_W is not set
# CONFIG_ULOG_OUTPUT_LVL_I is not set
CONFIG_ULOG_OUTPUT_LVL_D=y
CONFIG_ULOG_OUTPUT_LVL=7
CONFIG_ULOG_USING_ISR_LOG=y
CONFIG_ULOG_ASSERT_ENABLE=y
CONFIG_ULOG_LINE_BUF_SIZE=128
CONFIG_ULOG_USING_ASYNC_OUTPUT=y
CONFIG_ULOG_ASYNC_OUTPUT_BUF_SIZE=2048
CONFIG_ULOG_ASYNC_OUTPUT_BY_THREAD=y
CONFIG_ULOG_ASYNC_OUTPUT_THREAD_STACK=1024
CONFIG_ULOG_ASYNC_OUTPUT_THREAD_PRIORITY=30

#
# log format
#
# CONFIG_ULOG_OUTPUT_FLOAT is not set
CONFIG_ULOG_USING_COLOR=y
CONFIG_ULOG_OUTPUT_TIME=y
# CONFIG_ULOG_TIME_USING_TIMESTAMP is not set
CONFIG_ULOG_OUTPUT_LEVEL=y
CONFIG_ULOG_OUTPUT_TAG=y
# CONFIG_ULOG_OUTPUT_THREAD_NAME is not set
# end of log format

CONFIG_ULOG_BACKEND_USING_CONSOLE=y
# CONFIG_ULOG_BACKEND_USING_FILE is not set
# CONFIG_ULOG_USING_FILTER is not set
# CONFIG_ULOG_USING_SYSLOG is not set
# CONFIG_RT_USING_UTEST is not set
# CONFIG_RT_USING_VAR_EXPORT is not set
# CONFIG_RT_USING_RESOURCE_ID is not set
# CONFIG_RT_USING_ADT is not set
# CONFIG_RT_USING_RT_LINK is not set
# end of Utilities

# CONFIG_RT_USING_VBUS is not set

#
# Using USB legacy version
#
# CONFIG_RT_USING_USB_HOST is not set
# CONFIG_RT_USING_USB_DEVICE is not set
# end of Using USB legacy version

# CONFIG_RT_USING_FDT is not set
# end of RT-Thread Components

#
# RT-Thread online packages
#

#
# IoT - internet of things
#
# CONFIG_PKG_USING_LORAWAN_DRIVER is not set
# CONFIG_PKG_USING_PAHOMQTT is not set
# CONFIG_PKG_USING_UMQTT is not set
# CONFIG_PKG_USING_WEBCLIENT is not set
# CONFIG_PKG_USING_WEBNET is not set
# CONFIG_PKG_USING_MONGOOSE is not set
# CONFIG_PKG_USING_MYMQTT is not set
# CONFIG_PKG_USING_KAWAII_MQTT is not set
# CONFIG_PKG_USING_BC28_MQTT is not set
# CONFIG_PKG_USING_WEBTERMINAL is not set
# CONFIG_PKG_USING_FREEMODBUS is not set
# CONFIG_PKG_USING_NANOPB is not set
# CONFIG_PKG_USING_WIFI_HOST_DRIVER is not set

#
# Wi-Fi
#

#
# Marvell WiFi
#
# CONFIG_PKG_USING_WLANMARVELL is not set
# end of Marvell WiFi

#
# Wiced WiFi
#
# CONFIG_PKG_USING_WLAN_WICED is not set
# end of Wiced WiFi

# CONFIG_PKG_USING_RW007 is not set

#
# CYW43012 WiFi
#
# CONFIG_PKG_USING_WLAN_CYW43012 is not set
# end of CYW43012 WiFi

#
# BL808 WiFi
#
# CONFIG_PKG_USING_WLAN_BL808 is not set
# end of BL808 WiFi

#
# CYW43439 WiFi
#
# CONFIG_PKG_USING_WLAN_CYW43439 is not set
# end of CYW43439 WiFi
# end of Wi-Fi

# CONFIG_PKG_USING_COAP is not set
# CONFIG_PKG_USING_NOPOLL is not set
# CONFIG_PKG_USING_NETUTILS is not set
# CONFIG_PKG_USING_CMUX is not set
# CONFIG_PKG_USING_PPP_DEVICE is not set
# CONFIG_PKG_USING_AT_DEVICE is not set
# CONFIG_PKG_USING_ATSRV_SOCKET is not set
# CONFIG_PKG_USING_WIZNET is not set
# CONFIG_PKG_USING_ZB_COORDINATOR is not set

#
# IoT Cloud
#
# CONFIG_PKG_USING_ONENET is not set
# CONFIG_PKG_USING_GAGENT_CLOUD is not set
# CONFIG_PKG_USING_ALI_IOTKIT is not set
# CONFIG_PKG_USING_AZURE is not set
# CONFIG_PKG_USING_TENCENT_IOT_EXPLORER is not set
# CONFIG_PKG_USING_JIOT-C-SDK is not set
# CONFIG_PKG_USING_UCLOUD_IOT_SDK is not set
# CONFIG_PKG_USING_JOYLINK is not set
# CONFIG_PKG_USING_IOTSHARP_SDK is not set
# end of IoT Cloud

# CONFIG_PKG_USING_NIMBLE is not set
# CONFIG_PKG_USING_LLSYNC_SDK_ADAPTER is not set
# CONFIG_PKG_USING_OTA_DOWNLOADER is not set
# CONFIG_PKG_USING_IPMSG is not set
# CONFIG_PKG_USING_LSSDP is not set
# CONFIG_PKG_USING_AIRKISS_OPEN is not set
# CONFIG_PKG_USING_LIBRWS is not set
# CONFIG_PKG_USING_TCPSERVER is not set
# CONFIG_PKG_USING_PROTOBUF_C is not set
# CONFIG_PKG_USING_DLT645 is not set
# CONFIG_PKG_USING_QXWZ is not set
# CONFIG_PKG_USING_SMTP_CLIENT is not set
# CONFIG_PKG_USING_ABUP_FOTA is not set
# CONFIG_PKG_USING_LIBCURL2RTT is not set
# CONFIG_PKG_USING_CAPNP is not set
# CONFIG_PKG_USING_AGILE_TELNET is not set
# CONFIG_PKG_USING_NMEALIB is not set
# CONFIG_PKG_USING_PDULIB is not set
# CONFIG_PKG_USING_BTSTACK is not set
# CONFIG_PKG_USING_BT_CYW43012 is not set
# CONFIG_PKG_USING_CYW43XX is not set
# CONFIG_PKG_USING_LORAWAN_ED_STACK is not set
# CONFIG_PKG_USING_WAYZ_IOTKIT is not set
# CONFIG_PKG_USING_MAVLINK is not set
# CONFIG_PKG_USING_BSAL is not set
# CONFIG_PKG_USING_AGILE_MODBUS is not set
# CONFIG_PKG_USING_AGILE_FTP is not set
# CONFIG_PKG_USING_EMBEDDEDPROTO is not set
# CONFIG_PKG_USING_RT_LINK_HW is not set
# CONFIG_PKG_USING_RYANMQTT is not set
# CONFIG_PKG_USING_RYANW5500 is not set
# CONFIG_PKG_USING_LORA_PKT_FWD is not set
# CONFIG_PKG_USING_LORA_GW_DRIVER_LIB is not set
# CONFIG_PKG_USING_LORA_PKT_SNIFFER is not set
# CONFIG_PKG_USING_HM is not set
# CONFIG_PKG_USING_SMALL_MODBUS is not set
# CONFIG_PKG_USING_NET_SERVER is not set
# CONFIG_PKG_USING_ZFTP is not set
# CONFIG_PKG_USING_WOL is not set
# CONFIG_PKG_USING_ZEPHYR_POLLING is not set
# CONFIG_PKG_USING_MATTER_ADAPTATION_LAYER is not set
# CONFIG_PKG_USING_LHC_MODBUS is not set
# CONFIG_PKG_USING_QMODBUS is not set
# end of IoT - internet of things

#
# security packages
#
# CONFIG_PKG_USING_MBEDTLS is not set
# CONFIG_PKG_USING_LIBSODIUM is not set
# CONFIG_PKG_USING_LIBHYDROGEN is not set
# CONFIG_PKG_USING_TINYCRYPT is not set
# CONFIG_PKG_USING_TFM is not set
# CONFIG_PKG_USING_YD_CRYPTO is not set
# end of security packages

#
# language packages
#

#
# JSON: JavaScript Object Notation, a lightweight data-interchange format
#
# CONFIG_PKG_USING_CJSON is not set
# CONFIG_PKG_USING_LJSON is not set
# CONFIG_PKG_USING_RT_CJSON_TOOLS is not set
# CONFIG_PKG_USING_RAPIDJSON is not set
# CONFIG_PKG_USING_JSMN is not set
# CONFIG_PKG_USING_AGILE_JSMN is not set
# CONFIG_PKG_USING_PARSON is not set
# CONFIG_PKG_USING_RYAN_JSON is not set
# end of JSON: JavaScript Object Notation, a lightweight data-interchange format

#
# XML: Extensible Markup Language
#
# CONFIG_PKG_USING_SIMPLE_XML is not set
# CONFIG_PKG_USING_EZXML is not set
# end of XML: Extensible Markup Language

# CONFIG_PKG_USING_LUATOS_SOC is not set
# CONFIG_PKG_USING_LUA is not set
# CONFIG_PKG_USING_JERRYSCRIPT is not set
# CONFIG_PKG_USING_MICROPYTHON is not set
# CONFIG_PKG_USING_PIKASCRIPT is not set
# CONFIG_PKG_USING_RTT_RUST is not set
# end of language packages

#
# multimedia packages
#

#
# LVGL: powerful and easy-to-use embedded GUI library
#
# CONFIG_PKG_USING_LVGL is not set
# CONFIG_PKG_USING_LV_MUSIC_DEMO is not set
# CONFIG_PKG_USING_GUI_GUIDER_DEMO is not set
# end of LVGL: powerful and easy-to-use embedded GUI library

#
# u8g2: a monochrome graphic library
#
# CONFIG_PKG_USING_U8G2_OFFICIAL is not set
# CONFIG_PKG_USING_U8G2 is not set
# end of u8g2: a monochrome graphic library

# CONFIG_PKG_USING_OPENMV is not set
# CONFIG_PKG_USING_MUPDF is not set
# CONFIG_PKG_USING_STEMWIN is not set
# CONFIG_PKG_USING_WAVPLAYER is not set
# CONFIG_PKG_USING_TJPGD is not set
# CONFIG_PKG_USING_PDFGEN is not set
# CONFIG_PKG_USING_HELIX is not set
# CONFIG_PKG_USING_AZUREGUIX is not set
# CONFIG_PKG_USING_TOUCHGFX2RTT is not set
# CONFIG_PKG_USING_NUEMWIN is not set
# CONFIG_PKG_USING_MP3PLAYER is not set
# CONFIG_PKG_USING_TINYJPEG is not set
# CONFIG_PKG_USING_UGUI is not set
# CONFIG_PKG_USING_MCURSES is not set
# CONFIG_PKG_USING_TERMBOX is not set
# CONFIG_PKG_USING_VT100 is not set
# CONFIG_PKG_USING_QRCODE is not set
# CONFIG_PKG_USING_GUIENGINE is not set
# CONFIG_PKG_USING_3GPP_AMRNB is not set
# end of multimedia packages

#
# tools packages
#
# CONFIG_PKG_USING_CMBACKTRACE is not set
# CONFIG_PKG_USING_EASYFLASH is not set
# CONFIG_PKG_USING_EASYLOGGER is not set
# CONFIG_PKG_USING_SYSTEMVIEW is not set
# CONFIG_PKG_USING_SEGGER_RTT is not set
# CONFIG_PKG_USING_RTT_AUTO_EXE_CMD is not set
# CONFIG_PKG_USING_RDB is not set
# CONFIG_PKG_USING_ULOG_EASYFLASH is not set
# CONFIG_PKG_USING_LOGMGR is not set
# CONFIG_PKG_USING_ADBD is not set
# CONFIG_PKG_USING_COREMARK is not set
# CONFIG_PKG_USING_DHRYSTONE is not set
# CONFIG_PKG_USING_MEMORYPERF is not set
# CONFIG_PKG_USING_NR_MICRO_SHELL is not set
# CONFIG_PKG_USING_CHINESE_FONT_LIBRARY is not set
# CONFIG_PKG_USING_LUNAR_CALENDAR is not set
# CONFIG_PKG_USING_BS8116A is not set
# CONFIG_PKG_USING_GPS_RMC is not set
# CONFIG_PKG_USING_URLENCODE is not set
# CONFIG_PKG_USING_UMCN is not set
# CONFIG_PKG_USING_LWRB2RTT is not set
# CONFIG_PKG_USING_CPU_USAGE is not set
# CONFIG_PKG_USING_GBK2UTF8 is not set
# CONFIG_PKG_USING_VCONSOLE is not set
# CONFIG_PKG_USING_KDB is not set
# CONFIG_PKG_USING_WAMR is not set
# CONFIG_PKG_USING_MICRO_XRCE_DDS_CLIENT is not set
# CONFIG_PKG_USING_LWLOG is not set
# CONFIG_PKG_USING_ANV_TRACE is not set
# CONFIG_PKG_USING_ANV_MEMLEAK is not set
# CONFIG_PKG_USING_ANV_TESTSUIT is not set
# CONFIG_PKG_USING_ANV_BENCH is not set
# CONFIG_PKG_USING_DEVMEM is not set
# CONFIG_PKG_USING_REGEX is not set
# CONFIG_PKG_USING_MEM_SANDBOX is not set
# CONFIG_PKG_USING_SOLAR_TERMS is not set
# CONFIG_PKG_USING_GAN_ZHI is not set
# CONFIG_PKG_USING_FDT is not set
# CONFIG_PKG_USING_CBOX is not set
# CONFIG_PKG_USING_SNOWFLAKE is not set
# CONFIG_PKG_USING_HASH_MATCH is not set
# CONFIG_PKG_USING_ARMV7M_DWT_TOOL is not set
# CONFIG_PKG_USING_VOFA_PLUS is not set
# CONFIG_PKG_USING_ZDEBUG is not set
# end of tools packages

#
# system packages
#

#
# enhanced kernel services
#
# CONFIG_PKG_USING_RT_MEMCPY_CM is not set
# CONFIG_PKG_USING_RT_KPRINTF_THREADSAFE is not set
# CONFIG_PKG_USING_RT_VSNPRINTF_FULL is not set
# end of enhanced kernel services

# CONFIG_PKG_USING_AUNITY is not set

#
# acceleration: Assembly language or algorithmic acceleration packages
#
# CONFIG_PKG_USING_QFPLIB_M0_FULL is not set
# CONFIG_PKG_USING_QFPLIB_M0_TINY is not set
# CONFIG_PKG_USING_QFPLIB_M3 is not set
# end of acceleration: Assembly language or algorithmic acceleration packages

#
# CMSIS: ARM Cortex-M Microcontroller Software Interface Standard
#
# CONFIG_PKG_USING_CMSIS_5 is not set
# CONFIG_PKG_USING_CMSIS_CORE is not set
# CONFIG_PKG_USING_CMSIS_DSP is not set
# CONFIG_PKG_USING_CMSIS_NN is not set
# CONFIG_PKG_USING_CMSIS_RTOS1 is not set
# CONFIG_PKG_USING_CMSIS_RTOS2 is not set
# end of CMSIS: ARM Cortex-M Microcontroller Software Interface Standard

#
# Micrium: Micrium software products porting for RT-Thread
#
# CONFIG_PKG_USING_UCOSIII_WRAPPER is not set
# CONFIG_PKG_USING_UCOSII_WRAPPER is not set
# CONFIG_PKG_USING_UC_CRC is not set
# CONFIG_PKG_USING_UC_CLK is not set
# CONFIG_PKG_USING_UC_COMMON is not set
# CONFIG_PKG_USING_UC_MODBUS is not set
# end of Micrium: Micrium software products porting for RT-Thread

# CONFIG_PKG_USING_FREERTOS_WRAPPER is not set
# CONFIG_PKG_USING_LITEOS_SDK is not set
# CONFIG_PKG_USING_TZ_DATABASE is not set
# CONFIG_PKG_USING_CAIRO is not set
# CONFIG_PKG_USING_PIXMAN is not set
# CONFIG_PKG_USING_PARTITION is not set
# CONFIG_PKG_USING_PERF_COUNTER is not set
# CONFIG_PKG_USING_FILEX is not set
# CONFIG_PKG_USING_LEVELX is not set
CONFIG_PKG_USING_FLASHDB=y
CONFIG_PKG_FLASHDB_PATH="/packages/system/FlashDB"
CONFIG_FDB_USING_KVDB=y
# CONFIG_FDB_KV_AUTO_UPDATE is not set
# CONFIG_FDB_USING_TSDB is not set
CONFIG_FDB_USING_FAL_MODE=y
# CONFIG_FDB_WRITE_GRAN_1BIT is not set
# CONFIG_FDB_WRITE_GRAN_8BITS is not set
CONFIG_FDB_WRITE_GRAN_32BITS=y
CONFIG_FDB_WRITE_GRAN=32
CONFIG_FDB_NOT_USING_FILE_MODE=y
# CONFIG_FDB_USING_FILE_LIBC_MODE is not set
# CONFIG_FDB_USING_FILE_POSIX_MODE is not set
# CONFIG_FLASHDB_USING_SAMPLES is not set
CONFIG_FDB_DEBUG_ENABLE=y
# CONFIG_PKG_USING_FLASHDB_V10102 is not set
# CONFIG_PKG_USING_FLASHDB_V10101 is not set
# CONFIG_PKG_USING_FLASHDB_V10100 is not set
# CONFIG_PKG_USING_FLASHDB_V10000 is not set
CONFIG_PKG_USING_FLASHDB_LATEST_VERSION=y
CONFIG_PKG_FLASHDB_VER="latest"
CONFIG_PKG_FLASHDB_VER_NUM=0x99999
# CONFIG_PKG_USING_SQLITE is not set
# CONFIG_PKG_USING_RTI is not set
# CONFIG_PKG_USING_DFS_YAFFS is not set
# CONFIG_PKG_USING_LITTLEFS is not set
# CONFIG_PKG_USING_DFS_JFFS2 is not set
# CONFIG_PKG_USING_DFS_UFFS is not set
# CONFIG_PKG_USING_LWEXT4 is not set
# CONFIG_PKG_USING_THREAD_POOL is not set
# CONFIG_PKG_USING_ROBOTS is not set
# CONFIG_PKG_USING_EV is not set
# CONFIG_PKG_USING_SYSWATCH is not set
# CONFIG_PKG_USING_SYS_LOAD_MONITOR is not set
# CONFIG_PKG_USING_PLCCORE is not set
# CONFIG_PKG_USING_RAMDISK is not set
# CONFIG_PKG_USING_MININI is not set
# CONFIG_PKG_USING_QBOOT is not set
# CONFIG_PKG_USING_PPOOL is not set
# CONFIG_PKG_USING_OPENAMP is not set
# CONFIG_PKG_USING_RPMSG_LITE is not set
# CONFIG_PKG_USING_LPM is not set
# CONFIG_PKG_USING_TLSF is not set
# CONFIG_PKG_USING_EVENT_RECORDER is not set
# CONFIG_PKG_USING_ARM_2D is not set
# CONFIG_PKG_USING_MCUBOOT is not set
# CONFIG_PKG_USING_TINYUSB is not set
# CONFIG_PKG_USING_CHERRYUSB is not set
# CONFIG_PKG_USING_KMULTI_RTIMER is not set
# CONFIG_PKG_USING_TFDB is not set
# CONFIG_PKG_USING_QPC is not set
# CONFIG_PKG_USING_AGILE_UPGRADE is not set
# CONFIG_PKG_USING_FLASH_BLOB is not set
# CONFIG_PKG_USING_MLIBC is not set
# CONFIG_PKG_USING_TASK_MSG_BUS is not set
# CONFIG_PKG_USING_SFDB is not set
# CONFIG_PKG_USING_RTP is not set
# CONFIG_PKG_USING_REB is not set
# CONFIG_PKG_USING_R_RHEALSTONE is not set
# end of system packages

#
# peripheral libraries and drivers
#

#
# HAL & SDK Drivers
#

#
# STM32 HAL & SDK Drivers
#
# CONFIG_PKG_USING_STM32F4_HAL_DRIVER is not set
# CONFIG_PKG_USING_STM32F4_CMSIS_DRIVER is not set
# CONFIG_PKG_USING_STM32L4_HAL_DRIVER is not set
# CONFIG_PKG_USING_STM32L4_CMSIS_DRIVER is not set
# CONFIG_PKG_USING_STM32WB55_SDK is not set
# CONFIG_PKG_USING_STM32_SDIO is not set
# end of STM32 HAL & SDK Drivers

#
# Infineon HAL Packages
#
# CONFIG_PKG_USING_INFINEON_CAT1CM0P is not set
# CONFIG_PKG_USING_INFINEON_CMSIS is not set
# CONFIG_PKG_USING_INFINEON_CORE_LIB is not set
# CONFIG_PKG_USING_INFINEON_MTB_HAL_CAT1 is not set
# CONFIG_PKG_USING_INFINEON_MTB_PDL_CAT1 is not set
# CONFIG_PKG_USING_INFINEON_RETARGET_IO is not set
# CONFIG_PKG_USING_INFINEON_CAPSENSE is not set
# CONFIG_PKG_USING_INFINEON_CSDIDAC is not set
# CONFIG_PKG_USING_INFINEON_SERIAL_FLASH is not set
# CONFIG_PKG_USING_INFINEON_USBDEV is not set
# end of Infineon HAL Packages

# CONFIG_PKG_USING_BLUETRUM_SDK is not set
# CONFIG_PKG_USING_EMBARC_BSP is not set
# CONFIG_PKG_USING_ESP_IDF is not set

#
# Kendryte SDK
#
# CONFIG_PKG_USING_K210_SDK is not set
# CONFIG_PKG_USING_KENDRYTE_SDK is not set
# end of Kendryte SDK

# CONFIG_PKG_USING_NRF5X_SDK is not set
# CONFIG_PKG_USING_NRFX is not set
# CONFIG_PKG_USING_RASPBERRYPI_PICO_SDK is not set
# end of HAL & SDK Drivers

#
# sensors drivers
#
# CONFIG_PKG_USING_LSM6DSM is not set
# CONFIG_PKG_USING_LSM6DSL is not set
# CONFIG_PKG_USING_LPS22HB is not set
# CONFIG_PKG_USING_HTS221 is not set
# CONFIG_PKG_USING_LSM303AGR is not set
# CONFIG_PKG_USING_BME280 is not set
# CONFIG_PKG_USING_BME680 is not set
# CONFIG_PKG_USING_BMA400 is not set
# CONFIG_PKG_USING_BMI160_BMX160 is not set
# CONFIG_PKG_USING_SPL0601 is not set
# CONFIG_PKG_USING_MS5805 is not set
# CONFIG_PKG_USING_DA270 is not set
# CONFIG_PKG_USING_DF220 is not set
# CONFIG_PKG_USING_HSHCAL001 is not set
# CONFIG_PKG_USING_BH1750 is not set
# CONFIG_PKG_USING_MPU6XXX is not set
# CONFIG_PKG_USING_AHT10 is not set
# CONFIG_PKG_USING_AP3216C is not set
# CONFIG_PKG_USING_TSL4531 is not set
# CONFIG_PKG_USING_DS18B20 is not set
# CONFIG_PKG_USING_DHT11 is not set
# CONFIG_PKG_USING_DHTXX is not set
# CONFIG_PKG_USING_GY271 is not set
# CONFIG_PKG_USING_GP2Y10 is not set
# CONFIG_PKG_USING_SGP30 is not set
# CONFIG_PKG_USING_HDC1000 is not set
# CONFIG_PKG_USING_BMP180 is not set
# CONFIG_PKG_USING_BMP280 is not set
# CONFIG_PKG_USING_SHTC1 is not set
# CONFIG_PKG_USING_BMI088 is not set
# CONFIG_PKG_USING_HMC5883 is not set
# CONFIG_PKG_USING_MAX6675 is not set
# CONFIG_PKG_USING_TMP1075 is not set
# CONFIG_PKG_USING_SR04 is not set
# CONFIG_PKG_USING_CCS811 is not set
# CONFIG_PKG_USING_PMSXX is not set
# CONFIG_PKG_USING_RT3020 is not set
# CONFIG_PKG_USING_MLX90632 is not set
# CONFIG_PKG_USING_MLX90393 is not set
# CONFIG_PKG_USING_MLX90392 is not set
# CONFIG_PKG_USING_MLX90397 is not set
# CONFIG_PKG_USING_MS5611 is not set
# CONFIG_PKG_USING_MAX31865 is not set
# CONFIG_PKG_USING_VL53L0X is not set
# CONFIG_PKG_USING_INA260 is not set
# CONFIG_PKG_USING_MAX30102 is not set
# CONFIG_PKG_USING_INA226 is not set
# CONFIG_PKG_USING_LIS2DH12 is not set
# CONFIG_PKG_USING_HS300X is not set
# CONFIG_PKG_USING_ZMOD4410 is not set
# CONFIG_PKG_USING_ISL29035 is not set
# CONFIG_PKG_USING_MMC3680KJ is not set
# CONFIG_PKG_USING_QMP6989 is not set
# CONFIG_PKG_USING_BALANCE is not set
# CONFIG_PKG_USING_SHT2X is not set
# CONFIG_PKG_USING_SHT3X is not set
# CONFIG_PKG_USING_SHT4X is not set
# CONFIG_PKG_USING_AD7746 is not set
# CONFIG_PKG_USING_ADT74XX is not set
# CONFIG_PKG_USING_MAX17048 is not set
# CONFIG_PKG_USING_AS7341 is not set
# CONFIG_PKG_USING_CW2015 is not set
# CONFIG_PKG_USING_ICM20608 is not set
# CONFIG_PKG_USING_PAJ7620 is not set
# CONFIG_PKG_USING_STHS34PF80 is not set
# end of sensors drivers

#
# touch drivers
#
# CONFIG_PKG_USING_GT9147 is not set
# CONFIG_PKG_USING_GT1151 is not set
# CONFIG_PKG_USING_GT917S is not set
# CONFIG_PKG_USING_GT911 is not set
# CONFIG_PKG_USING_FT6206 is not set
# CONFIG_PKG_USING_FT5426 is not set
# CONFIG_PKG_USING_FT6236 is not set
# CONFIG_PKG_USING_XPT2046_TOUCH is not set
# CONFIG_PKG_USING_CST816X is not set
# CONFIG_PKG_USING_CST812T is not set
# end of touch drivers

# CONFIG_PKG_USING_REALTEK_AMEBA is not set
# CONFIG_PKG_USING_BUTTON is not set
# CONFIG_PKG_USING_PCF8574 is not set
# CONFIG_PKG_USING_SX12XX is not set
# CONFIG_PKG_USING_SIGNAL_LED is not set
# CONFIG_PKG_USING_LEDBLINK is not set
# CONFIG_PKG_USING_LITTLED is not set
# CONFIG_PKG_USING_LKDGUI is not set
# CONFIG_PKG_USING_INFRARED is not set
# CONFIG_PKG_USING_MULTI_INFRARED is not set
# CONFIG_PKG_USING_AGILE_BUTTON is not set
# CONFIG_PKG_USING_AGILE_LED is not set
# CONFIG_PKG_USING_AT24CXX is not set
# CONFIG_PKG_USING_MOTIONDRIVER2RTT is not set
# CONFIG_PKG_USING_PCA9685 is not set
# CONFIG_PKG_USING_ILI9341 is not set
# CONFIG_PKG_USING_I2C_TOOLS is not set
# CONFIG_PKG_USING_NRF24L01 is not set
# CONFIG_PKG_USING_RPLIDAR is not set
# CONFIG_PKG_USING_AS608 is not set
# CONFIG_PKG_USING_RC522 is not set
# CONFIG_PKG_USING_WS2812B is not set
# CONFIG_PKG_USING_EXTERN_RTC_DRIVERS is not set
# CONFIG_PKG_USING_MULTI_RTIMER is not set
# CONFIG_PKG_USING_MAX7219 is not set
# CONFIG_PKG_USING_BEEP is not set
# CONFIG_PKG_USING_EASYBLINK is not set
# CONFIG_PKG_USING_PMS_SERIES is not set
# CONFIG_PKG_USING_CAN_YMODEM is not set
# CONFIG_PKG_USING_LORA_RADIO_DRIVER is not set
# CONFIG_PKG_USING_QLED is not set
# CONFIG_PKG_USING_AGILE_CONSOLE is not set
# CONFIG_PKG_USING_LD3320 is not set
# CONFIG_PKG_USING_WK2124 is not set
# CONFIG_PKG_USING_LY68L6400 is not set
# CONFIG_PKG_USING_DM9051 is not set
# CONFIG_PKG_USING_SSD1306 is not set
# CONFIG_PKG_USING_QKEY is not set
# CONFIG_PKG_USING_RS485 is not set
# CONFIG_PKG_USING_RS232 is not set
# CONFIG_PKG_USING_NES is not set
# CONFIG_PKG_USING_VIRTUAL_SENSOR is not set
# CONFIG_PKG_USING_VDEVICE is not set
# CONFIG_PKG_USING_SGM706 is not set
# CONFIG_PKG_USING_RDA58XX is not set
# CONFIG_PKG_USING_LIBNFC is not set
# CONFIG_PKG_USING_MFOC is not set
# CONFIG_PKG_USING_TMC51XX is not set
# CONFIG_PKG_USING_TCA9534 is not set
# CONFIG_PKG_USING_KOBUKI is not set
# CONFIG_PKG_USING_ROSSERIAL is not set
# CONFIG_PKG_USING_MICRO_ROS is not set
# CONFIG_PKG_USING_MCP23008 is not set
# CONFIG_PKG_USING_MISAKA_AT24CXX is not set
# CONFIG_PKG_USING_MISAKA_RGB_BLING is not set
# CONFIG_PKG_USING_LORA_MODEM_DRIVER is not set
# CONFIG_PKG_USING_SOFT_SERIAL is not set
# CONFIG_PKG_USING_MB85RS16 is not set
# CONFIG_PKG_USING_RFM300 is not set
# CONFIG_PKG_USING_IO_INPUT_FILTER is not set
# CONFIG_PKG_USING_LRF_NV7LIDAR is not set
# CONFIG_PKG_USING_AIP650 is not set
# CONFIG_PKG_USING_FINGERPRINT is not set
# CONFIG_PKG_USING_BT_ECB02C is not set
# CONFIG_PKG_USING_UAT is not set
# CONFIG_PKG_USING_ST7789 is not set
# CONFIG_PKG_USING_VS1003 is not set
# CONFIG_PKG_USING_X9555 is not set
# CONFIG_PKG_USING_SYSTEM_RUN_LED is not set
# CONFIG_PKG_USING_BT_MX01 is not set
# CONFIG_PKG_USING_RGPOWER is not set
# CONFIG_PKG_USING_BT_MX02 is not set
# CONFIG_PKG_USING_SPI_TOOLS is not set
# end of peripheral libraries and drivers

#
# AI packages
#
# CONFIG_PKG_USING_LIBANN is not set
# CONFIG_PKG_USING_NNOM is not set
# CONFIG_PKG_USING_ONNX_BACKEND is not set
# CONFIG_PKG_USING_ONNX_PARSER is not set
# CONFIG_PKG_USING_TENSORFLOWLITEMICRO is not set
# CONFIG_PKG_USING_ELAPACK is not set
# CONFIG_PKG_USING_ULAPACK is not set
# CONFIG_PKG_USING_QUEST is not set
# CONFIG_PKG_USING_NAXOS is not set
# CONFIG_PKG_USING_R_TINYMAIX is not set
# end of AI packages

#
# Signal Processing and Control Algorithm Packages
#
# CONFIG_PKG_USING_APID is not set
# CONFIG_PKG_USING_FIRE_PID_CURVE is not set
# CONFIG_PKG_USING_QPID is not set
# CONFIG_PKG_USING_UKAL is not set
# CONFIG_PKG_USING_DIGITALCTRL is not set
# CONFIG_PKG_USING_KISSFFT is not set
# end of Signal Processing and Control Algorithm Packages

#
# miscellaneous packages
#

#
# project laboratory
#
# end of project laboratory

#
# samples: kernel and components samples
#
# CONFIG_PKG_USING_KERNEL_SAMPLES is not set
# CONFIG_PKG_USING_FILESYSTEM_SAMPLES is not set
# CONFIG_PKG_USING_NETWORK_SAMPLES is not set
# CONFIG_PKG_USING_PERIPHERAL_SAMPLES is not set
# end of samples: kernel and components samples

#
# entertainment: terminal games and other interesting software packages
#
# CONFIG_PKG_USING_CMATRIX is not set
# CONFIG_PKG_USING_SL is not set
# CONFIG_PKG_USING_CAL is not set
# CONFIG_PKG_USING_ACLOCK is not set
# CONFIG_PKG_USING_THREES is not set
# CONFIG_PKG_USING_2048 is not set
# CONFIG_PKG_USING_SNAKE is not set
# CONFIG_PKG_USING_TETRIS is not set
# CONFIG_PKG_USING_DONUT is not set
# CONFIG_PKG_USING_COWSAY is not set
# CONFIG_PKG_USING_MORSE is not set
# end of entertainment: terminal games and other interesting software packages

# CONFIG_PKG_USING_LIBCSV is not set
CONFIG_PKG_USING_OPTPARSE=y
CONFIG_PKG_OPTPARSE_PATH="/packages/misc/optparse"
CONFIG_PKG_USING_OPTPARSE_LATEST_VERSION=y
CONFIG_PKG_OPTPARSE_VER="latest"
# CONFIG_OPTPARSE_USING_DEMO is not set
# CONFIG_PKG_USING_FASTLZ is not set
# CONFIG_PKG_USING_MINILZO is not set
# CONFIG_PKG_USING_QUICKLZ is not set
# CONFIG_PKG_USING_LZMA is not set
# CONFIG_PKG_USING_RALARAM is not set
# CONFIG_PKG_USING_MULTIBUTTON is not set
# CONFIG_PKG_USING_FLEXIBLE_BUTTON is not set
# CONFIG_PKG_USING_CANFESTIVAL is not set
# CONFIG_PKG_USING_ZLIB is not set
# CONFIG_PKG_USING_MINIZIP is not set
# CONFIG_PKG_USING_HEATSHRINK is not set
# CONFIG_PKG_USING_DSTR is not set
# CONFIG_PKG_USING_TINYFRAME is not set
# CONFIG_PKG_USING_KENDRYTE_DEMO is not set
# CONFIG_PKG_USING_UPACKER is not set
# CONFIG_PKG_USING_UPARAM is not set
# CONFIG_PKG_USING_HELLO is not set
# CONFIG_PKG_USING_VI is not set
# CONFIG_PKG_USING_KI is not set
# CONFIG_PKG_USING_ARMv7M_DWT is not set
# CONFIG_PKG_USING_CRCLIB is not set
# CONFIG_PKG_USING_LWGPS is not set
# CONFIG_PKG_USING_STATE_MACHINE is not set
# CONFIG_PKG_USING_DESIGN_PATTERN is not set
# CONFIG_PKG_USING_CONTROLLER is not set
# CONFIG_PKG_USING_PHASE_LOCKED_LOOP is not set
# CONFIG_PKG_USING_MFBD is not set
# CONFIG_PKG_USING_SLCAN2RTT is not set
# CONFIG_PKG_USING_SOEM is not set
# CONFIG_PKG_USING_QPARAM is not set
# CONFIG_PKG_USING_CorevMCU_CLI is not set
# end of miscellaneous packages

#
# Arduino libraries
#
# CONFIG_PKG_USING_RTDUINO is not set

#
# Projects and Demos
#
# CONFIG_PKG_USING_ARDUINO_MSGQ_C_CPP_DEMO is not set
# CONFIG_PKG_USING_ARDUINO_SKETCH_LOADER_DEMO is not set
# CONFIG_PKG_USING_ARDUINO_ULTRASOUND_RADAR is not set
# CONFIG_PKG_USING_ARDUINO_NINEINONE_SENSOR_SHIELD is not set
# CONFIG_PKG_USING_ARDUINO_SENSOR_KIT is not set
# CONFIG_PKG_USING_ARDUINO_MATLAB_SUPPORT is not set
# end of Projects and Demos

#
# Sensors
#
# CONFIG_PKG_USING_ARDUINO_SENSOR_DEVICE_DRIVERS is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SENSOR is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SENSORLAB is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ADXL375 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VL53L0X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VL53L1X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VL6180X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MAX31855 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MAX31865 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MAX31856 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MAX6675 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MLX90614 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LSM9DS1 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AHTX0 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LSM9DS0 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP280 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ADT7410 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP085 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BME680 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP9808 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP4728 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_INA219 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LTR390 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ADXL345 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_DHT is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP9600 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LSM6DS is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BNO055 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MAX1704X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MMC56X3 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MLX90393 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MLX90395 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ICM20X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_DPS310 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_HTS221 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SHT4X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SHT31 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ADXL343 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BME280 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AS726X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AMG88XX is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AM2320 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AM2315 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LTR329_LTR303 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP085_UNIFIED is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP183 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP183_UNIFIED is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BMP3XX is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MS8607 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LIS3MDL is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MLX90640 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MMA8451 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MSA301 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MPL115A2 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BNO08X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BNO08X_RVC is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LIS2MDL is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LSM303DLH_MAG is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LC709203F is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_CAP1188 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_CCS811 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_NAU7802 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LIS331 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LPS2X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LPS35HW is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LSM303_ACCEL is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_LIS3DH is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PCF8591 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MPL3115A2 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MPR121 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MPRLS is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MPU6050 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PCT2075 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PM25AQI is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_EMC2101 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_FXAS21002C is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SCD30 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_FXOS8700 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_HMC5883_UNIFIED is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SGP30 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TMP006 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TLA202X is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TCS34725 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SI7021 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SI1145 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SGP40 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SHTC3 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_HDC1000 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_HTU21DF is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AS7341 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_HTU31D is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_INA260 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TMP007_LIBRARY is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_L3GD20 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TMP117 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TSC2007 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TSL2561 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TSL2591_LIBRARY is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VCNL4040 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VEML6070 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VEML6075 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_VEML7700 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_LIS3DHTR is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_DHT is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_ADXL335 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_ADXL345 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_BME280 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_BMP280 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_H3LIS331DL is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_MMA7660 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_TSL2561 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_PAJ7620 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_VL53L0X is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_ITG3200 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_SHT31 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_HP20X is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_DRV2605L is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_BBM150 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_HMC5883L is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_LSM303DLH is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_TCS3414CS is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_MP503 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_BMP085 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_HIGHTEMP is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_VEML6070 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_SI1145 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_SHT35 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_AT42QT1070 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_LSM6DS3 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_HDC1000 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_HM3301 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_MCP9600 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_LTC2941 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_LDC1612 is not set
# CONFIG_PKG_USING_ARDUINO_CAPACITIVESENSOR is not set
# CONFIG_PKG_USING_ARDUINO_JARZEBSKI_MPU6050 is not set
# end of Sensors

#
# Display
#
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_GFX_LIBRARY is not set
# CONFIG_PKG_USING_ARDUINO_U8G2 is not set
# CONFIG_PKG_USING_ARDUINO_TFT_ESPI is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ST7735 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SSD1306 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ILI9341 is not set
# CONFIG_PKG_USING_SEEED_TM1637 is not set
# end of Display

#
# Timing
#
# CONFIG_PKG_USING_ARDUINO_RTCLIB is not set
# CONFIG_PKG_USING_ARDUINO_MSTIMER2 is not set
# CONFIG_PKG_USING_ARDUINO_TICKER is not set
# CONFIG_PKG_USING_ARDUINO_TASKSCHEDULER is not set
# end of Timing

#
# Data Processing
#
# CONFIG_PKG_USING_ARDUINO_KALMANFILTER is not set
# CONFIG_PKG_USING_ARDUINO_ARDUINOJSON is not set
# CONFIG_PKG_USING_ARDUINO_TENSORFLOW_LITE_MICRO is not set
# CONFIG_PKG_USING_ARDUINO_RUNNINGMEDIAN is not set
# end of Data Processing

#
# Data Storage
#

#
# Communication
#
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PN532 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SI4713 is not set
# end of Communication

#
# Device Control
#
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PCF8574 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_PCA9685 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TPA2016 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_DRV2605 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_DS1841 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_DS3502 is not set
# CONFIG_PKG_USING_ARDUINO_SEEED_PCF85063TP is not set
# end of Device Control

#
# Other
#
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MFRC630 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_SI5351 is not set
# end of Other

#
# Signal IO
#
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BUSIO is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_TCA8418 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP23017 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_ADS1X15 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_AW9523 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP3008 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_MCP4725 is not set
# CONFIG_PKG_USING_ARDUINO_ADAFRUIT_BD3491FS is not set
# end of Signal IO

#
# Uncategorized
#
# end of Arduino libraries
# end of RT-Thread online packages

#
# Hardware Drivers Config
#
CONFIG_SOC_N32G452XX=y

#
# Onboard Peripheral Drivers
#
CONFIG_BSP_USING_UART=y
# end of Onboard Peripheral Drivers

#
# On-chip Peripheral Drivers
#
# CONFIG_N32G45X_PIN_NUMBERS_48 is not set
# CONFIG_N32G45X_PIN_NUMBERS_64 is not set
CONFIG_N32G45X_PIN_NUMBERS_100=y
# CONFIG_N32G45X_PIN_NUMBERS_128 is not set
CONFIG_N32G45X_PIN_NUMBERS=100
CONFIG_BSP_USING_GPIO=y

#
# Remap JTAG Port
#
# CONFIG_BSP_RMP_SW_JTAG_FULL_ENABLE is not set
# CONFIG_BSP_RMP_SW_JTAG_NO_NJTRST is not set
CONFIG_BSP_RMP_SW_JTAG_SW_ENABLE=y
# CONFIG_BSP_RMP_SW_JTAG_DISABLE is not set
# end of Remap JTAG Port

CONFIG_BSP_USING_ON_CHIP_FLASH=y
CONFIG_BSP_USING_WDT=y
CONFIG_BSP_USING_UART1=y
# CONFIG_BSP_USING_UART1_PIN_RMP is not set
CONFIG_BSP_USING_UART1_NO_RMP=y
CONFIG_BSP_USING_UART2=y
CONFIG_BSP_USING_UART2_NO_RMP=y
# CONFIG_BSP_USING_UART2_PIN_RMP1 is not set
# CONFIG_BSP_USING_UART2_PIN_RMP2 is not set
# CONFIG_BSP_USING_UART2_PIN_RMP3 is not set
CONFIG_BSP_USING_UART3=y
CONFIG_BSP_USING_UART3_PIN_NO_RMP=y
# CONFIG_BSP_USING_UART3_PIN_PART_RMP is not set
# CONFIG_BSP_USING_UART3_PIN_ALL_RMP is not set
CONFIG_BSP_USING_UART4=y
CONFIG_BSP_USING_UART4_PIN_NORMP=y
# CONFIG_BSP_USING_UART4_PIN_RMP1 is not set
# CONFIG_BSP_USING_UART4_PIN_RMP2 is not set
# CONFIG_BSP_USING_UART4_PIN_RMP3 is not set
CONFIG_BSP_USING_UART5=y
CONFIG_BSP_USING_UART5_PIN_NORMP=y
# CONFIG_BSP_USING_UART5_PIN_RMP1 is not set
# CONFIG_BSP_USING_UART5_PIN_RMP2 is not set
# CONFIG_BSP_USING_UART5_PIN_RMP3 is not set
CONFIG_BSP_USING_UART6=y
CONFIG_BSP_USING_UART6_PIN_NORMP=y
# CONFIG_BSP_USING_UART6_PIN_RMP2 is not set
# CONFIG_BSP_USING_UART6_PIN_RMP3 is not set
CONFIG_BSP_USING_UART7=y
CONFIG_BSP_USING_UART7_PIN_NORMP=y
# CONFIG_BSP_USING_UART7_PIN_RMP1 is not set
# CONFIG_BSP_USING_UART7_PIN_RMP3 is not set
# CONFIG_BSP_USING_PWM is not set
CONFIG_BSP_USING_HWTIMER=y
# CONFIG_BSP_USING_HWTIM3 is not set
CONFIG_BSP_USING_HWTIM4=y
# CONFIG_BSP_USING_HWTIM5 is not set
# CONFIG_BSP_USING_HWTIM6 is not set
# CONFIG_BSP_USING_HWTIM7 is not set
# CONFIG_BSP_USING_SPI is not set
# CONFIG_BSP_USING_I2C1 is not set
# CONFIG_BSP_USING_ADC is not set
# CONFIG_BSP_USING_CAN is not set
# CONFIG_BSP_USING_SDIO is not set
# end of On-chip Peripheral Drivers
# end of Hardware Drivers Config
