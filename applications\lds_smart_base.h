/**
 * @file lds_smart_base.h
 * @brief LDS Smart base Communication Protocol Stack Header File
 * @details This header file contains declarations for smart base communication protocol functions
 *          and data structures following the specified protocol format with big-endian byte order
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Format:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(variable) + CRC16(2)
 * - Big-endian byte order (MSB first)
 * - Head: Fixed 0x5AA5 for all frames
 * - SEQ: 0x00-0xFF sequence number for request/response matching
 * - CMD: Function code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - CRC16: 16-bit CRC checksum (CRC16 of all bytes from Head to CRC16-1)
 */

#ifndef __APPLICATIONS_LDS_SMART_BASE_H__
#define __APPLICATIONS_LDS_SMART_BASE_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define SMART_BASE_FRAME_HEAD1          0x5A
#define SMART_BASE_FRAME_HEAD2          0xA5
#define SMART_BASE_MIN_FRAME_LEN       8      /**< Minimum frame length without DATA */
#define SMART_BASE_MAX_FRAME_LEN       64     /**< Maximum frame length */
#define SMART_BASE_MAX_DATA_LEN        (SMART_BASE_MAX_FRAME_LEN - SMART_BASE_MIN_FRAME_LEN)

/* ================================ Type Definitions ======================== */

/**
 * @brief Smart base command enumeration
 * @details Defines the supported command types for smart base communication
 */
typedef enum {
    LDS_SMART_BASE_CMD_KEY = 0x01,          /**< PPT key command */
    LDS_SMART_BASE_CMD_VERSION = 0x02,      /**< Query version information command */
    LDS_SMART_BASE_CMD_STATUS = 0x03,       /**< Query status information command */
    LDS_SMART_BASE_CMD_MAX,
} LDS_SMART_BASE_CMD_E;

/**
 * @brief Protocol frame structure
 * @details Structure representing a complete protocol frame
 */
typedef struct {
    uint8_t head1;                           /**< Frame header (0x5A) */
    uint8_t head2;                           /**< Frame header (0xA5) */
    uint8_t cmd;                           /**< Command code (big-endian) */
    uint8_t seq;                            /**< Sequence number */
    uint16_t dataLen;                       /**< Data length (big-endian) */
    uint8_t data[SMART_BASE_MAX_DATA_LEN];  /**< Data payload (max 245 bytes) */
    uint16_t crc16;                         /**< Frame CRC16 checksum */
} lds_smart_base_frame_t;

/* ================================ Function Declarations =================== */

/**
 * @brief Get smart base version information
 * @details Retrieves the version information of the smart base device
 *
 * @return const char* Pointer to version string
 */
const char *ldsSmartBaseGetVersion(void);
/**
 * @brief Initialize smart base communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete smart base system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and command retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 *
 * @example
 * @code
 * int result = ldsSmartBaseInit();
 * if (result != 0) {
 *     rt_kprintf("Smart base initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsSmartBaseInit(void);

/**
 * @brief Deinitialize smart base communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function should be called before system shutdown or when
 *       smart base communication is no longer needed
 */
int ldsSmartBaseDeinit(void);

/**
 * @brief Query device version information
 * @details Requests version information from the specified device
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note The response will contain version information in the data field
 */
int ldsSmartBaseQueryVersion(void);

/**
 * @brief Query device status information
 * @details Requests status information from the specified device
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note The response will contain status information in the data field
 */
int ldsSmartBaseQueryStatus(void);

/**
 * @brief Set smart base select mode
 * @details Set smart base select mode
 *
 * @param mode select mode
 * @return int 0 on success, negative error code on failure
 */

int ldsSmartBaseSetSelectMode(int8_t mode);
/**
 * @brief Get smart base status
 * @details Get smart base status
 *
 * @return uint8_t status
 */
uint8_t ldsSmartBaseGetStatus(void);
/**
 * @brief Get smart base power state
 * @details Retrieves the current power state of the smart base
 *
 * @return int 0 or 1 on success, negative error code on failure
 */
int ldsSmartBaseGetPowerCtrl(void);
/**
 * @brief Control smart base power
 * @details Turn on or off the smart base device via power control pin
 *
 * @param on True to turn on, false to turn off
 * @return int 0 on success, negative error code on failure
 */
int ldsSmartBaseSetPowerCtrl(bool on);
#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_SMART_BASE_H__ */
