/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2015-07-29     Arda.Fu      first implementation
 */
#include <stdint.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <ulog.h>
#include "drv_spi.h"
#include "drv_sound.h"
#include "lds_utils.h"
#include "lds_kvdb.h"
#include "lds_key.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_digital_tube.h"
#include "lds_bk9535.h"
#include "lds_mic.h"
#include "lds_smart_base.h"
#include "lds_third_party.h"


#define MODEL_ID "AUX-EDUAudioProcessor-0001"


#define POWER_CTRL_PIN_DSP      "PB.0"
#define POWER_CTRL_PIN_AMP      "PE.1"
#define POWER_CTRL_PIN_USB      "PD.0"
#define POWER_CTRL_PIN_LINEOUT  "PB.1"

#define POWER_OFF_DETECT_PIN    "PD.3"

#define WDT_DEVICE_NAME    "wdt"    /* 看门狗设备名称 */
#define WDT_TIMEOUT         6       /* 看门狗超时时间 秒 */

static rt_device_t wdg_dev;         /* 看门狗设备句柄 */
static rt_base_t power_ctrl_DSP = -1;  /* DSP电源控制引脚 */
static rt_base_t power_ctrl_amp = -1; /* 功放电源控制引脚 */
static rt_base_t power_ctrl_usb = -1; /* usb电源控制引脚 */
static rt_base_t power_ctrl_lineout = -1; /* lineout mute控制引脚 */
static bool powerOff = false;

static void idleHook(void)
{
    /* 在空闲线程的回调函数里喂狗 */
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    // rt_kprintf("feed the dog!\n ");
}
void ldsWdtFeed(void)
{
    /* 喂狗 */
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
}
static int ldsWdtInit(void)
{
    // return RT_EOK;
    rt_err_t ret = RT_EOK;
    rt_uint32_t timeout = WDT_TIMEOUT;        /* 溢出时间，单位：秒 */

    /* 根据设备名称查找看门狗设备，获取设备句柄 */
    wdg_dev = rt_device_find(WDT_DEVICE_NAME);
    if (!wdg_dev)
    {
        LOG_E("find %s failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 初始化设备 */
    rt_device_init(wdg_dev);
    /* 设置看门狗溢出时间 */
    ret = rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_SET_TIMEOUT, &timeout);
    if (ret != RT_EOK)
    {
        LOG_E("set %s timeout failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 启动看门狗 */
    ret = rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL);
    if (ret != RT_EOK)
    {
        LOG_E("start %s failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 设置空闲线程回调函数 */
    rt_thread_idle_sethook(idleHook);

    return ret;
}

/* 中断回调函数 */
void amp_mute(void *args)
{
    powerOff = true;
    rt_kprintf("turn off amp and lineout!\n");

    rt_pin_write(power_ctrl_amp, PIN_HIGH);
    rt_pin_write(power_ctrl_lineout, PIN_LOW);
}
int ldsPoweroffDetectInit(void)
{
    rt_base_t pin = rt_pin_get(POWER_OFF_DETECT_PIN);
    if (pin < 0)
    {
        LOG_E("Get pin %s failed %d !", POWER_OFF_DETECT_PIN, pin);
        return -RT_ERROR;
    }

    rt_pin_mode(pin, PIN_MODE_INPUT);
    /* 绑定中断，下降沿模式，回调函数名为amp_mute */
    rt_pin_attach_irq(pin, PIN_IRQ_MODE_RISING, amp_mute, RT_NULL);
    rt_pin_irq_enable(pin, PIN_IRQ_ENABLE);
    return RT_EOK;
}
int main(void)
{
    uint32_t count = 0;
    LOG_I("Starting %s main process", MODEL_ID);
    ulog_flush(); // Flush any buffered log messages
    ulog_async_output_enabled(false); // Disable async output for initialization
    //power ctrl pin init
    power_ctrl_DSP = power_ctrl_pin_init(POWER_CTRL_PIN_DSP, PIN_HIGH);
    power_ctrl_amp = power_ctrl_pin_init(POWER_CTRL_PIN_AMP, PIN_LOW);
    power_ctrl_usb = power_ctrl_pin_init(POWER_CTRL_PIN_USB, PIN_HIGH);
    power_ctrl_lineout = power_ctrl_pin_init(POWER_CTRL_PIN_LINEOUT, PIN_HIGH);

    ldsKvdbInit();
    /* init digital tube */
    ldsDigitalTubeInit();
    /* init poweroff detect */
    ldsPoweroffDetectInit();
    /* init led*/
    ldsLedInit();
    /* init UAC communication */
    ldsUacInit();
    /* init smart base communication */
    ldsSmartBaseInit();
    /* init DSP communication */
    // ldsDSPInit();
    /* init freeless mic communication */
    ldsMicInit();
    /* init watchdog*/
    if(ldsWdtInit() != RT_EOK)
    {
        if(ldsWdtInit() != RT_EOK)
        {
            if(ldsWdtInit() != RT_EOK)
            {
                LOG_E("Watchdog init failed!, reboot system!");
                rt_thread_mdelay(1000);
                rt_hw_cpu_reset(); // reset system
            }
        }
    }
    /* init UHF transmission */
    lds_bk9535_bk9529_init();
    /* init key */
    ldsKeyInit();
    /* init third party communication */
    ldsThirdPartyInit();
    /* init factory test */
    // ldsFactoryTestInit();
    ulog_async_output_enabled(true);

    ldsMicQueryVersion();
    ldsSmartBaseQueryVersion();
    while (1)
    {
        //防止掉电侦测抖动
        if(powerOff) {
            rt_thread_mdelay(600);
            powerOff = false;
            rt_pin_write(power_ctrl_amp, PIN_LOW);
            rt_pin_write(power_ctrl_lineout, PIN_HIGH);
        }
        // 10s
        if(count % 10 == 0){
            ldsMicQueryStatus();
            lds_bk9535_prevent_rf_unlock();
            count = 0;
        }
        rt_thread_mdelay(1000);
        count++;
    }
}

