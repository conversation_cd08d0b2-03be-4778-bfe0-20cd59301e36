/**
 * @file lds_mic.h
 * @brief LDS Microphone Communication Protocol Stack Header File
 * @details This header file contains declarations for microphone communication protocol functions
 *          and data structures following the specified protocol format with big-endian byte order
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-21
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Specification:
 * - Frame Format: Head(1) + modelId(2) + CMD(2) + SEQ(1) + Addr(1) + LEN(2) + DATA(variable) + SUM(1)
 * - Big-endian byte order (MSB first)
 * - Head: Fixed 0x5A for all frames
 * - modelId: Device unique identifier (0x0001-0xFFFFF)
 * - SEQ: Sequence number for request/response matching (0x00-0xFF)
 * - Addr: Address field (Host=0x00, Slave=0x01/0x02/0x03, etc.)
 * - CMD: Function code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - SUM: Checksum (sum of all bytes from Head to SUM-1, lower 8 bits)
 */

#ifndef __APPLICATIONS_LDS_MIC_H__
#define __APPLICATIONS_LDS_MIC_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define MIC_FRAME_HEAD          0xA5
#define MIC_MIN_FRAME_LEN       10      /**< Minimum frame length without DATA */
#define MIC_MAX_FRAME_LEN       128     /**< Maximum frame length */
#define MIC_MAX_DATA_LEN        (MIC_MAX_FRAME_LEN - MIC_MIN_FRAME_LEN)

/* ================================ Device Model IDs ======================= */
/**
 * @brief Device model ID definitions
 * @details Predefined model IDs for different device types
 */
#define LDS_MIC_MODEL_AMPLIFIER_HOST    0x0101  /**< Amplifier host device */
#define LDS_MIC_MODEL_ARRAY_MIC         0x0102  /**< Array microphone device */

/* ================================ Address Definitions ==================== */
/**
 * @brief Protocol address definitions
 * @details Standard addresses used in the communication protocol
 */
#define LDS_MIC_ADDR_HOST               0x00    /**< Host device address */
#define LDS_MIC_ADDR_SLAVE_1            0x01    /**< Slave device 1 address */
#define LDS_MIC_ADDR_SLAVE_2            0x02    /**< Slave device 2 address */
#define LDS_MIC_ADDR_SLAVE_3            0x03    /**< Slave device 3 address */
#define LDS_MIC_ADDR_SLAVE_BROADCAST    0xF1    /**< Slave broadcast address */
#define LDS_MIC_ADDR_UNUSED             0xEE    /**< Unused address */
#define LDS_MIC_ADDR_GLOBAL_BROADCAST   0xFF    /**< Global broadcast address */

/* ================================ Type Definitions ======================== */

/**
 * @brief Microphone command enumeration
 * @details Defines the supported command types for microphone communication
 */
typedef enum {
    LDS_MIC_CMD_FACTORY_RESET = 0x0201,     /**< Factory reset command */
    LDS_MIC_CMD_HEARTBEAT = 0x0202,         /**< Heartbeat command */
    LDS_MIC_CMD_REBOOT = 0x0203,            /**< Reboot device command */
    LDS_MIC_CMD_VERSION_QUERY = 0x0204,     /**< Query version information command */
    LDS_MIC_CMD_SET_ARRAY_PARAMS = 0x0301,  /**< Set array microphone parameters command */
} LDS_MIC_CMD_E;

/**
 * @brief Array microphone parameter types
 * @details Parameter types for the SET_ARRAY_PARAMS command
 */
typedef enum {
    LDS_MIC_PARAM_MUTE_CONTROL = 0x01,      /**< Mute control parameter */
    LDS_MIC_PARAM_SOUND_MODE = 0x02,        /**< Sound mode parameter */
} LDS_MIC_PARAM_TYPE_E;

/**
 * @brief Mute control states
 * @details Values for mute control parameter
 */
typedef enum {
    LDS_MIC_MUTE_OFF = 0x00,                /**< Unmute state */
    LDS_MIC_MUTE_ON = 0x01,                 /**< Mute state */
    LDS_MIC_MUTE_QUERY = 0xFF,              /**< Query current mute state */
    LDS_MIC_MUTE_MAX,                       /**< Maximum value for validation */
} LDS_MIC_MUTE_E;

/**
 * @brief Sound mode types
 * @details Values for sound mode parameter
 */
typedef enum {
    LDS_MIC_SOUND_MODE_STANDARD = 0x00,     /**< Standard sound mode */
    LDS_MIC_SOUND_MODE_FEMALE = 0x01,       /**< Female voice optimized mode */
    LDS_MIC_SOUND_MODE_MALE = 0x02,         /**< Male voice optimized mode */
    LDS_MIC_SOUND_MODE_QUERY = 0xFF,        /**< Query current sound mode */
    LDS_MIC_SOUND_MODE_MAX,                 /**< Maximum value for validation */
} LDS_MIC_SOUND_MODE_E;

/**
 * @brief Protocol frame structure
 * @details Structure representing a complete protocol frame
 */
typedef struct {
    uint8_t head;                           /**< Frame header (0x5A) */
    uint16_t modelId;                       /**< Device model ID (big-endian) */
    uint16_t cmd;                           /**< Command code (big-endian) */
    uint8_t seq;                            /**< Sequence number */
    uint8_t addr;                           /**< Address field */
    uint16_t dataLen;                       /**< Data length (big-endian) */
    uint8_t data[MIC_MAX_DATA_LEN];         /**< Data payload (max 246 bytes) */
    uint8_t checksum;                       /**< Frame checksum */
} lds_mic_frame_t;

/* ================================ Function Declarations =================== */

/**
 * @brief Get microphone status
 * @details Retrieves the current status of the microphone
 *
 * @return uint8_t Status byte
 */
uint8_t ldsMicGetStatus(void);
/**
 * @brief Get microphone power state
 * @details Retrieves the current power state of the microphone
 *
 * @return int 0 or 1 on success, negative error code on failure
 */
int ldsMicGetPowerCtrl(void);
/**
 * @brief Control microphone power
 * @details Turns microphone power on or off
 *
 * @param on true to turn on, false to turn off
 * @return int 0 on success, negative error code on failure
 */
int ldsMicSetPowerCtrl(int on);
/**
 * @brief Get microphone version information
 * @details Retrieves the version information of the microphone device
 *
 * @return const char* Pointer to version string
 */
const char *ldsMicGetVersion(void);
/**
 * @brief Initialize microphone communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete microphone system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and command retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 *
 * @example
 * @code
 * int result = ldsMicInit();
 * if (result != 0) {
 *     rt_kprintf("Microphone initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsMicInit(void);

/**
 * @brief Deinitialize microphone communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function should be called before system shutdown or when
 *       microphone communication is no longer needed
 */
int ldsMicDeinit(void);

/**
 * @brief Query device version information
 * @details Requests version information from the specified device
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note The response will contain version information in the data field
 */
int ldsMicQueryVersion(void);

/**
 * @brief Query device status information
 * @details Requests status information from the specified device
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note The response will contain status information in the data field
 */
int ldsMicQueryStatus(void);

/**
 * @brief Set microphone mute control
 * @details Controls the mute state of the microphone array
 *
 * @param addr Target device address
 * @return int 0 on success, negative error code on failure
 */
int ldsMicSetMuteByAddr(uint8_t addr);
/**
 * @brief Set microphone mute control
 * @details Controls the mute state of the microphone array
 *
 * @param addr Target device address
 * @param muteState Mute state from LDS_MIC_MUTE_E
 * @return int 0 on success, negative error code on failure
 *
 * @example
 * @code
 * // Mute the microphone
 * int result = ldsMicSetMuteControl(LDS_MIC_ADDR_SLAVE_1, 
 *                                   LDS_MIC_MUTE_ON);
 * @endcode
 */
int ldsMicSetMuteControl(uint8_t addr, LDS_MIC_MUTE_E muteState);

/**
 * @brief Set microphone sound mode
 * @details Sets the sound processing mode of the microphone array
 *
 * @param soundMode Sound mode from LDS_MIC_SOUND_MODE_E
 * @return int 0 on success, negative error code on failure
 *
 * @example
 * @code
 * // Set female voice optimized mode
 * int result = ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_FEMALE);
 * @endcode
 */
int ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_E soundMode);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_MIC_H__ */
