#include <rtthread.h>
#include <rtdevice.h>
#include "n32g45x.h"
#include "lds_utils.h"

#define DBG_TAG "UTILS"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>


rt_base_t power_ctrl_pin_init(const char *pin, rt_uint8_t value)
{
    rt_base_t power_tmp = rt_pin_get(pin);
    if (power_tmp < 0)
    {
        LOG_E("Get pin %s failed %d !", pin, power_tmp);
        return -RT_ERROR;
    }
    rt_pin_mode(power_tmp, PIN_MODE_OUTPUT);
    rt_pin_write(power_tmp, value);
    return power_tmp;
}


uint8_t ldsUtilCheckXor(const uint8_t *data, size_t len)
{
    uint8_t checksum = 0;
    if (data == RT_NULL || len == 0) {
        LOG_E("Invalid data or size");
        return 0;
    }
    for (size_t i = 0; i < len; i++)
    {
        checksum ^= data[i];
    }
    return checksum;
}

uint8_t ldsUtilCheckSum(const uint8_t *data, size_t len)
{
    uint8_t sum = 0;
    
    if (data == RT_NULL || len == 0) {
        LOG_E("Invalid data or size");
        return 0;
    }
    
    for (size_t i = 0; i < len; i++) {
        sum += data[i];
    }
    
    return sum;
}

uint32_t ldsUtilReverseBits(uint32_t dword, size_t len)
{
    if (len == 0 || len > sizeof(dword))
    {
        LOG_E("Invalid length for bit reversal: %zu bytes. Must be between 1 and %zu.", len, sizeof(dword));
        return 0;
    }

    switch (len)
    {
        case 1: // 8-bit reversal
            dword = ((dword >> 1) & 0x55) | ((dword & 0x55) << 1);
            dword = ((dword >> 2) & 0x33) | ((dword & 0x33) << 2);
            dword = ((dword >> 4) & 0x0F) | ((dword & 0x0F) << 4);
            return dword;

        case 2: // 16-bit reversal
            dword = ((dword >> 1) & 0x5555) | ((dword & 0x5555) << 1);
            dword = ((dword >> 2) & 0x3333) | ((dword & 0x3333) << 2);
            dword = ((dword >> 4) & 0x0F0F) | ((dword & 0x0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF) | ((dword & 0x00FF) << 8);
            return dword;
        
        case 4: // 32-bit reversal
            dword = ((dword >> 1) & 0x55555555) | ((dword & 0x55555555) << 1);
            dword = ((dword >> 2) & 0x33333333) | ((dword & 0x33333333) << 2);
            dword = ((dword >> 4) & 0x0F0F0F0F) | ((dword & 0x0F0F0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF00FF) | ((dword & 0x00FF00FF) << 8);
            dword = (dword >> 16) | (dword << 16);
            return dword;

        default: // Fallback for other cases (e.g., 3 bytes).
        {
            const size_t num_bits = len * 8;
            uint32_t reversed_dword = 0;
            for (size_t i = 0; i < num_bits; i++)
            {
                reversed_dword <<= 1;
                reversed_dword |= (dword & 1);
                dword >>= 1;
            }
            return reversed_dword;
        }
    }
}
static rt_mutex_t g_crc_mutex = RT_NULL;
uint16_t ldsUtilCheckCrc16(const uint8_t *data, size_t len)
{
    uint32_t index = 0;
    uint16_t crc = 0;
    if (data == RT_NULL || len == 0) {
        LOG_E("Invalid data or size");
        return 0;
    }
    if(g_crc_mutex == RT_NULL){
        g_crc_mutex = rt_mutex_create("crc_mutex", RT_IPC_FLAG_PRIO);
        if (g_crc_mutex == RT_NULL)
        {
            LOG_E("create crc_mutex failed.");
        }
    }
    rt_mutex_take(g_crc_mutex, RT_WAITING_FOREVER);
    CRC->CRC16D = 0xFFFF;
    
    for (index = 0; index < len; index++)
    {
        CRC->CRC16DAT = ldsUtilReverseBits(data[index], sizeof(data[index]));
    }
    crc = (CRC->CRC16D);
    rt_mutex_release(g_crc_mutex);
    return ldsUtilReverseBits(crc, sizeof(crc));
}
